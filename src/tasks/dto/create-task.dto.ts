import { IsEnum, <PERSON>NotEmpty, IsString } from 'class-validator';
import { TaskStatus } from '../task.model';

export class CreateTaskDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsEnum(TaskStatus)
  status: TaskStatus;

  userId: string;

  //   @IsOptional()
  //   @ValidateNested({ each: true })
  //   @Type(() => CreateTaskLabelDto)
  //   labels?: CreateTaskLabelDto[];
}
