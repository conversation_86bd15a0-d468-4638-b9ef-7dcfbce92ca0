import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
  BadRequestException,
  HttpCode,
} from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { FindOneParams } from './find-one.params';
import { Task } from './entities/task.entity';
import { WrongTaskStatusException } from './exceptions/wrong-task-status.exception';
import { UserNotFoundException } from './exceptions/user-not-found.exception';
import { CreateTaskLabelDto } from './dto/create-task-label.dto';

@Controller('tasks')
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  public async create(@Body() createTaskDto: CreateTaskDto): Promise<Task> {
    try {
      return await this.tasksService.createTask(createTaskDto);
    } catch (error) {
      if (error instanceof UserNotFoundException) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
  }

  @Post(':id/labels')
  public async addLabels(
    @Param() params: FindOneParams,
    @Body() labels: CreateTaskLabelDto[],
  ): Promise<Task> {
    if (!labels.length) {
      throw new BadRequestException('No labels provided');
    }

    const task = await this.findOneOrFail(params.id);
    return await this.tasksService.addLabels(task, labels);
  }

  @Delete(':id/labels')
  @HttpCode(204)
  public async removeLabels(
    @Param() params: FindOneParams,
    @Body() labels: string[],
  ): Promise<void> {
    if (!labels.length) {
      throw new BadRequestException('No labels provided');
    }

    const task = await this.findOneOrFail(params.id);
    await this.tasksService.removeLabels(task, labels);
  }

  @Get()
  public async findAll(): Promise<Task[]> {
    return await this.tasksService.findAll();
  }

  @Get(':id')
  public async findOne(@Param() params: FindOneParams): Promise<Task> {
    return await this.findOneOrFail(params.id);
  }

  @Patch(':id')
  public async update(
    @Param() params: FindOneParams,
    @Body() updateTaskDto: UpdateTaskDto,
  ): Promise<Task> {
    const task = await this.findOneOrFail(params.id);
    try {
      return await this.tasksService.updateTask(task, updateTaskDto);
    } catch (error) {
      if (error instanceof WrongTaskStatusException) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
  }

  @Delete(':id')
  @HttpCode(204)
  public async delete(@Param() params: FindOneParams): Promise<void> {
    const task = await this.findOneOrFail(params.id);
    try {
      await this.tasksService.deleteTask(task);
    } catch (error) {
      throw error;
    }
  }

  private async findOneOrFail(id: string): Promise<Task> {
    const task = await this.tasksService.findOne(id);
    if (!task) {
      throw new NotFoundException();
    }
    return task;
  }
}
